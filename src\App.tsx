import React from "react";

// EMERGENCY MINIMAL APP FOR DEBUGGING
function App() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
      color: 'white',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '2rem',
      textAlign: 'center'
    }}>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '20px',
        padding: '3rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        maxWidth: '600px'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          marginBottom: '1rem',
          background: 'linear-gradient(45deg, #00ffff, #ff00ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          Econic Media
        </h1>
        <h2 style={{
          fontSize: '1.5rem',
          marginBottom: '2rem',
          color: '#00ffff'
        }}>
          Luxury Web Design & Product Photography
        </h2>
        <p style={{
          fontSize: '1.1rem',
          lineHeight: '1.6',
          marginBottom: '2rem',
          opacity: 0.9
        }}>
          🚀 Website emergency recovery in progress...
          <br />
          All luxury glassmorphism features will be restored shortly.
        </p>
        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          <div style={{
            background: 'rgba(0, 255, 255, 0.1)',
            padding: '1rem',
            borderRadius: '10px',
            border: '1px solid rgba(0, 255, 255, 0.3)'
          }}>
            ✨ Modern Web Design
          </div>
          <div style={{
            background: 'rgba(255, 0, 255, 0.1)',
            padding: '1rem',
            borderRadius: '10px',
            border: '1px solid rgba(255, 0, 255, 0.3)'
          }}>
            📸 Product Photography
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
